package com.tutoring.service;

import com.tutoring.entity.User;
import com.tutoring.repository.UserRepository;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class CalendlyOAuthService {

    private final UserRepository userRepository;
    private final WebClient.Builder webClientBuilder;

    @Value("${calendly.oauth.client-id}")
    private String clientId;

    @Value("${calendly.oauth.client-secret}")
    private String clientSecret;

    @Value("${calendly.oauth.redirect-uri}")
    private String redirectUri;

    public void exchangeCodeForToken(String code, String userEmail) {
        WebClient webClient = webClientBuilder
                .baseUrl("https://auth.calendly.com")
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .build();

        Map<String, String> tokenRequest = Map.of(
                "grant_type", "authorization_code",
                "code", code,
                "redirect_uri", redirectUri,
                "client_id", clientId,
                "client_secret", clientSecret
        );

        webClient.post()
                .uri("/oauth/token")
                .bodyValue(buildFormData(tokenRequest))
                .retrieve()
                .bodyToMono(CalendlyTokenResponse.class)
                .flatMap(tokenResponse -> {
                    // Get user info with the access token
                    return getUserInfo(tokenResponse.getAccess_token())
                            .map(userInfo -> {
                                // Update user in database
                                updateUserCalendlyInfo(userEmail, tokenResponse.getAccess_token(), userInfo.getResource().getUri());
                                return tokenResponse;
                            });
                })
                .doOnSuccess(response -> log.info("Successfully connected Calendly for user: {}", userEmail))
                .doOnError(error -> log.error("Failed to exchange code for token for user: {}", userEmail, error))
                .block(); // Block for synchronous processing
    }

    private Mono<CalendlyUserInfoResponse> getUserInfo(String accessToken) {
        WebClient webClient = webClientBuilder
                .baseUrl("https://api.calendly.com")
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();

        return webClient.get()
                .uri("/users/me")
                .retrieve()
                .bodyToMono(CalendlyUserInfoResponse.class);
    }

    private void updateUserCalendlyInfo(String userEmail, String accessToken, String calendlyUri) {
        User user = userRepository.findByEmail(userEmail)
                .orElseThrow(() -> new RuntimeException("User not found: " + userEmail));

        user.setCalendlyAccessToken(accessToken);
        user.setCalendlyUri(calendlyUri);
        userRepository.save(user);

        log.info("Updated Calendly info for user: {}", userEmail);
    }

    public void disconnectUser(String userEmail) {
        User user = userRepository.findByEmail(userEmail)
                .orElseThrow(() -> new RuntimeException("User not found: " + userEmail));

        user.setCalendlyAccessToken(null);
        user.setCalendlyUri(null);
        userRepository.save(user);

        log.info("Disconnected Calendly for user: {}", userEmail);
    }

    public void simulateCalendlyConnection(String userEmail) {
        User user = userRepository.findByEmail(userEmail)
                .orElseThrow(() -> new RuntimeException("User not found: " + userEmail));

        // Simulate Calendly connection with mock data
        user.setCalendlyAccessToken("mock_access_token_" + System.currentTimeMillis());
        user.setCalendlyUri("https://api.calendly.com/users/mock_user_" + user.getId());
        userRepository.save(user);

        log.info("Simulated Calendly connection for user: {}", userEmail);
    }

    private String buildFormData(Map<String, String> data) {
        StringBuilder formData = new StringBuilder();
        for (Map.Entry<String, String> entry : data.entrySet()) {
            if (formData.length() > 0) {
                formData.append("&");
            }
            formData.append(entry.getKey()).append("=").append(entry.getValue());
        }
        return formData.toString();
    }

    @Data
    private static class CalendlyTokenResponse {
        private String access_token;
        private String token_type;
        private String refresh_token;
        private String scope;
        private Long expires_in;
    }

    @Data
    private static class CalendlyUserInfoResponse {
        private CalendlyUserInfo resource;
    }

    @Data
    private static class CalendlyUserInfo {
        private String uri;
        private String name;
        private String email;
        private String current_organization;
    }
}
