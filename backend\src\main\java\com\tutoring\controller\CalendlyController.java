package com.tutoring.controller;

import com.tutoring.service.CalendlyService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api/calendly")
@RequiredArgsConstructor
@CrossOrigin(origins = "http://localhost:3000")
public class CalendlyController {

    private final CalendlyService calendlyService;

    @GetMapping("/user")
    public Mono<ResponseEntity<CalendlyService.CalendlyUser>> getCurrentUser() {
        return calendlyService.getCurrentUser()
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.badRequest().build());
    }

    @GetMapping("/organization")
    public Mono<ResponseEntity<CalendlyService.CalendlyOrganization>> getOrganization() {
        return calendlyService.getOrganization()
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.badRequest().build());
    }

    @GetMapping("/event-types")
    public Mono<ResponseEntity<java.util.List<CalendlyService.CalendlyEventType>>> getEventTypes(@RequestParam String userUri) {
        return calendlyService.getEventTypes(userUri)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.badRequest().build());
    }

    @GetMapping("/event-types/{userEmail}")
    public Mono<ResponseEntity<java.util.List<CalendlyService.CalendlyEventType>>> getEventTypesForUser(@PathVariable String userEmail) {
        // This endpoint will fetch event types using the user's stored access token
        // Implementation would need to fetch user from database and use their token
        return Mono.just(ResponseEntity.ok(java.util.Collections.emptyList()));
    }

    @PostMapping("/create-scheduling-link")
    public Mono<ResponseEntity<CalendlyService.CalendlySchedulingLink>> createSchedulingLink(
            @RequestParam String eventTypeUri,
            @RequestParam String studentEmail,
            @RequestParam String studentName) {
        
        return calendlyService.createSchedulingLink(eventTypeUri, studentEmail, studentName)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.badRequest().build());
    }

    @PostMapping("/invite-user")
    public Mono<ResponseEntity<CalendlyService.CalendlyUser>> inviteUser(
            @RequestParam String email,
            @RequestParam String firstName,
            @RequestParam String lastName) {
        
        return calendlyService.createOrganizationInvitation(email, firstName, lastName)
                .map(ResponseEntity::ok)
                .onErrorReturn(ResponseEntity.badRequest().build());
    }
}
