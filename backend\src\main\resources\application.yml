server:
  port: 8080

spring:
  application:
    name: calendly-tutoring-platform
  
  datasource:
    url: jdbc:h2:mem:tutoring_db
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    
  h2:
    console:
      enabled: true
      path: /h2-console
      
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    format-sql: true
    database-platform: org.hibernate.dialect.H2Dialect
    
  mail:
    host: smtp.gmail.com
    port: 587
    username: ${MAIL_USERNAME:<EMAIL>}
    password: ${MAIL_PASSWORD:your-app-password}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

# Calendly API Configuration
calendly:
  api:
    base-url: https://api.calendly.com
    # For OAuth-based authentication, this token will be obtained per user
    token: ${CALENDLY_TOKEN:}
    # These will be fetched dynamically via OAuth
    organization-uri: ${CALENDLY_ORGANIZATION_URI:}
    user-uri: ${CALENDLY_USER_URI:}
  
  # OAuth Configuration
  oauth:
    # Replace with your actual Calendly OAuth Client ID
    client-id: ${CALENDLY_CLIENT_ID:jXBmXGnJW7M26tJSiA3DDRYOXq2QiCSyvXzFZ3LdbHo}
    # Replace with your actual Calendly OAuth Client Secret
    client-secret: ${CALENDLY_CLIENT_SECRET:RFBUthmJQNRqhlrJXf0d7-5N_yFgAX29cdw2vkfgL6o}
    # Redirect URI for OAuth callback
    redirect-uri: ${CALENDLY_REDIRECT_URI:http://localhost:8080/api/auth/calendly/callback}
    # Authorization URL
    auth-url: https://auth.calendly.com/oauth/authorize
    # Token URL
    token-url: https://auth.calendly.com/oauth/token
  
  # Webhook Configuration
  webhook:
    # Replace with your actual Calendly Webhook Key
    signing-key: ${CALENDLY_WEBHOOK_KEY:uyY788rtpeZU4QD2jt_prUrVkxagURSPhHH__pCUKUU}
    # Webhook endpoint URL
    endpoint-url: ${CALENDLY_WEBHOOK_ENDPOINT:http://localhost:8080/api/webhooks/calendly}

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:mySecretKey}
  expiration: 86400000 # 24 hours

# Logging
logging:
  level:
    com.tutoring: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
