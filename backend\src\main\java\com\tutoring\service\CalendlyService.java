package com.tutoring.service;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class CalendlyService {

    private final WebClient webClient;
    private final String calendlyToken;
    private final String organizationUri;
    private final String userUri;

    public CalendlyService(@Value("${calendly.api.base-url}") String baseUrl,
                          @Value("${calendly.api.token}") String token,
                          @Value("${calendly.api.organization-uri}") String organizationUri,
                          @Value("${calendly.api.user-uri}") String userUri) {
        this.calendlyToken = token;
        this.organizationUri = organizationUri;
        this.userUri = userUri;
        this.webClient = WebClient.builder()
                .baseUrl(baseUrl)
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + token)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();
    }

    public Mono<CalendlyUser> createOrganizationInvitation(String email, String firstName, String lastName) {
        Map<String, Object> requestBody = Map.of(
                "email", email,
                "first_name", firstName,
                "last_name", lastName
        );

        return webClient.post()
                .uri("/organization_invitations")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(CalendlyInvitationResponse.class)
                .map(response -> {
                    CalendlyUser user = new CalendlyUser();
                    user.setUri(response.getResource().getUser().getUri());
                    user.setEmail(email);
                    user.setName(firstName + " " + lastName);
                    return user;
                })
                .doOnError(error -> log.error("Error creating Calendly invitation: {}", error.getMessage()));
    }

    public Mono<List<CalendlyAvailability>> getUserAvailability(String userUri, LocalDateTime startTime, LocalDateTime endTime) {
        String start = startTime.atZone(ZoneId.systemDefault()).format(DateTimeFormatter.ISO_INSTANT);
        String end = endTime.atZone(ZoneId.systemDefault()).format(DateTimeFormatter.ISO_INSTANT);

        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/user_availability_schedules")
                        .queryParam("user", userUri)
                        .queryParam("start_time", start)
                        .queryParam("end_time", end)
                        .build())
                .retrieve()
                .bodyToMono(CalendlyAvailabilityResponse.class)
                .map(CalendlyAvailabilityResponse::getCollection)
                .doOnError(error -> log.error("Error fetching availability: {}", error.getMessage()));
    }

    public Mono<CalendlyEvent> createEvent(CalendlyEventRequest request) {
        return webClient.post()
                .uri("/scheduled_events")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(CalendlyEventResponse.class)
                .map(CalendlyEventResponse::getResource)
                .doOnError(error -> log.error("Error creating Calendly event: {}", error.getMessage()));
    }

    public Mono<Void> cancelEvent(String eventUri) {
        return webClient.delete()
                .uri("/scheduled_events/{uri}", eventUri)
                .retrieve()
                .bodyToMono(Void.class)
                .doOnError(error -> log.error("Error cancelling Calendly event: {}", error.getMessage()));
    }

    public Mono<List<CalendlyEventType>> getEventTypes(String userUri) {
        return webClient.get()
                .uri(uriBuilder -> uriBuilder
                        .path("/event_types")
                        .queryParam("user", userUri)
                        .build())
                .retrieve()
                .bodyToMono(CalendlyEventTypesResponse.class)
                .map(CalendlyEventTypesResponse::getCollection)
                .doOnError(error -> log.error("Error fetching event types: {}", error.getMessage()));
    }

    // Overloaded method that accepts user's access token
    public Mono<List<CalendlyEventType>> getEventTypes(String userUri, String accessToken) {
        return WebClient.builder()
                .baseUrl("https://sandbox.api.calendly.com") // Use sandbox for consistency
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken)
                .build()
                .get()
                .uri(uriBuilder -> uriBuilder
                        .path("/event_types")
                        .queryParam("user", userUri)
                        .build())
                .retrieve()
                .bodyToMono(CalendlyEventTypesResponse.class)
                .map(CalendlyEventTypesResponse::getCollection)
                .doOnError(error -> log.error("Error fetching event types with user token: {}", error.getMessage()));
    }

    // Get current user info
    public Mono<CalendlyUser> getCurrentUser() {
        return webClient.get()
                .uri("/users/me")
                .retrieve()
                .bodyToMono(CalendlyUserResponse.class)
                .map(CalendlyUserResponse::getResource)
                .doOnError(error -> log.error("Error fetching current user: {}", error.getMessage()));
    }

    // Get organization info
    public Mono<CalendlyOrganization> getOrganization() {
        return getCurrentUser()
                .flatMap(user -> {
                    String orgUri = user.getCurrent_organization();
                    return webClient.get()
                            .uri(orgUri.replace("https://api.calendly.com", ""))
                            .retrieve()
                            .bodyToMono(CalendlyOrganizationResponse.class)
                            .map(CalendlyOrganizationResponse::getResource);
                })
                .doOnError(error -> log.error("Error fetching organization: {}", error.getMessage()));
    }

    // Create a single-use scheduling link
    public Mono<CalendlySchedulingLink> createSchedulingLink(String eventTypeUri, String studentEmail, String studentName) {
        Map<String, Object> requestBody = Map.of(
                "resource_type", "scheduling_link",
                "owner", eventTypeUri,
                "owner_type", "EventType"
        );

        return webClient.post()
                .uri("/scheduling_links")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(CalendlySchedulingLinkResponse.class)
                .map(CalendlySchedulingLinkResponse::getResource)
                .doOnError(error -> log.error("Error creating scheduling link: {}", error.getMessage()));
    }

    // Overloaded method that accepts user's access token
    public Mono<CalendlySchedulingLink> createSchedulingLink(String eventTypeUri, String studentEmail, String studentName, String accessToken) {
        Map<String, Object> requestBody = Map.of(
                "resource_type", "scheduling_link",
                "owner", eventTypeUri,
                "owner_type", "EventType"
        );

        return WebClient.builder()
                .baseUrl("https://sandbox.api.calendly.com") // Use sandbox for consistency
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken)
                .build()
                .post()
                .uri("/scheduling_links")
                .bodyValue(requestBody)
                .retrieve()
                .bodyToMono(CalendlySchedulingLinkResponse.class)
                .map(CalendlySchedulingLinkResponse::getResource)
                .doOnError(error -> log.error("Error creating scheduling link with user token: {}", error.getMessage()));
    }

    // Create Event Type in Calendly (NEW API SUPPORT)
    public Mono<CalendlyEventType> createEventType(String accessToken, CalendlyEventTypeCreateRequest request) {
        return WebClient.builder()
                .baseUrl("https://sandbox.api.calendly.com") // Use sandbox for testing
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken)
                .build()
                .post()
                .uri("/event_types")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(CalendlyEventTypeResponse.class)
                .map(CalendlyEventTypeResponse::getResource)
                .doOnError(error -> log.error("Error creating event type: {}", error.getMessage()));
    }

    // Schedule Event Directly (SANDBOX API)
    public Mono<CalendlyEvent> scheduleEvent(String accessToken, CalendlyScheduleEventRequest request) {
        return WebClient.builder()
                .baseUrl("https://sandbox.api.calendly.com") // Sandbox supports direct scheduling
                .defaultHeader(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken)
                .build()
                .post()
                .uri("/scheduled_events")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(CalendlyEventResponse.class)
                .map(CalendlyEventResponse::getResource)
                .doOnError(error -> log.error("Error scheduling event: {}", error.getMessage()));
    }



    @Data
    public static class CalendlyUser {
        private String uri;
        private String email;
        private String name;
        private String current_organization;
    }

    @Data
    public static class CalendlyAvailability {
        private String start;
        private String end;
        private String status;
    }

    @Data
    public static class CalendlyEvent {
        private String uri;
        private String name;
        private String start_time;
        private String end_time;
        private String status;
        private String event_type;
        private List<CalendlyInvitee> invitees;
    }

    @Data
    public static class CalendlyInvitee {
        private String uri;
        private String email;
        private String name;
        private String status;
    }

    @Data
    public static class CalendlyEventType {
        private String uri;
        private String name;
        private String description;
        private Integer duration;
        private String scheduling_url;
    }

    @Data
    public static class CalendlyEventRequest {
        private String event_type;
        private String start_time;
        private String end_time;
        private List<CalendlyInviteeRequest> invitees;
    }

    @Data
    public static class CalendlyInviteeRequest {
        private String email;
        private String name;
    }

    @Data
    private static class CalendlyInvitationResponse {
        private CalendlyInvitationResource resource;
    }

    @Data
    private static class CalendlyInvitationResource {
        private CalendlyUser user;
    }

    @Data
    private static class CalendlyAvailabilityResponse {
        private List<CalendlyAvailability> collection;
    }

    @Data
    private static class CalendlyEventResponse {
        private CalendlyEvent resource;
    }

    @Data
    private static class CalendlyEventTypesResponse {
        private List<CalendlyEventType> collection;
    }

    @Data
    private static class CalendlyUserResponse {
        private CalendlyUser resource;
    }

    @Data
    public static class CalendlyOrganization {
        private String uri;
        private String name;
    }

    @Data
    private static class CalendlyOrganizationResponse {
        private CalendlyOrganization resource;
    }

    @Data
    public static class CalendlySchedulingLink {
        private String booking_url;
        private String owner;
        private String owner_type;
    }

    @Data
    private static class CalendlySchedulingLinkResponse {
        private CalendlySchedulingLink resource;
    }

    // Request classes for creating event types (based on new API documentation)
    @Data
    public static class CalendlyEventTypeCreateRequest {
        private Boolean active = true;
        private String owner; // required - user URI
        private String name; // required
        private String description;
        private Integer duration; // required - minutes (1-720)
        private List<CalendlyLocationConfiguration> locations;
        private String color = "#0069ff"; // hex color
        private String locale = "en";
    }

    @Data
    public static class CalendlyLocationConfiguration {
        private String kind; // ask_invitee, custom, google_conference, etc.
        private String location;
        private String additional_info;
        private String phone_number;
    }

    @Data
    private static class CalendlyEventTypeResponse {
        private CalendlyEventType resource;
    }

    // Request classes for scheduling events directly (SANDBOX API)
    @Data
    public static class CalendlyScheduleEventRequest {
        private String invitee_email; // required
        private String event_type; // required - event type URI
        private String start_time; // required - ISO 8601 UTC format
        private String timezone; // required - e.g., "Asia/Kolkata"
        private CalendlyEventLocation location;
        private List<CalendlyQuestionAnswer> questions_and_answers;
    }

    @Data
    public static class CalendlyEventLocation {
        private String type; // inbound_call, zoom, google_conference, custom
        private String phone_number; // for inbound_call
        private String location; // for custom
    }

    @Data
    public static class CalendlyQuestionAnswer {
        private String question;
        private String answer;
    }

}
