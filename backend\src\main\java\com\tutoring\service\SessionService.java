package com.tutoring.service;

import com.tutoring.dto.*;
import com.tutoring.entity.*;
import com.tutoring.mapper.*;
import com.tutoring.repository.*;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Service
@RequiredArgsConstructor
@Slf4j
public class SessionService {

    private final SessionEnquiryRepository sessionEnquiryRepository;
    private final TutoringSessionRepository tutoringSessionRepository;
    private final UserRepository userRepository;
    private final SessionMapper sessionMapper;
    private final CalendlyService calendlyService;
    private final EmailService emailService;

    // Session Enquiry Operations
    @Transactional
    public SessionEnquiryDto createSessionEnquiry(String studentEmail, SessionEnquiryCreateDto dto) {
        User student = userRepository.findByEmail(studentEmail)
                .orElseThrow(() -> new RuntimeException("Student not found"));
        
        User tutor = userRepository.findById(dto.getTutorId())
                .orElseThrow(() -> new RuntimeException("Tutor not found"));

        // Check if tutor is available for the requested time
        List<TutoringSession> conflictingSessions = tutoringSessionRepository
                .findByTutorAndTimeRange(tutor, dto.getRequestedStartTime(), dto.getRequestedEndTime());
        
        if (!conflictingSessions.isEmpty()) {
            throw new RuntimeException("Tutor is not available for the requested time slot");
        }

        SessionEnquiry enquiry = new SessionEnquiry();
        enquiry.setStudent(student);
        enquiry.setTutor(tutor);
        enquiry.setSubject(dto.getSubject());
        enquiry.setRequestedStartTime(dto.getRequestedStartTime());
        enquiry.setRequestedEndTime(dto.getRequestedEndTime());
        enquiry.setMessage(dto.getMessage());
        enquiry.setStatus(SessionEnquiry.EnquiryStatus.PENDING);

        SessionEnquiry savedEnquiry = sessionEnquiryRepository.save(enquiry);
        
        // Send notification email to tutor
        emailService.sendEnquiryNotification(tutor, savedEnquiry);
        
        return sessionMapper.toDto(savedEnquiry);
    }

    @Transactional
    public SessionEnquiryDto respondToEnquiry(Long enquiryId, String tutorEmail, SessionEnquiryResponseDto response) {
        SessionEnquiry enquiry = sessionEnquiryRepository.findById(enquiryId)
                .orElseThrow(() -> new RuntimeException("Enquiry not found"));

        if (!enquiry.getTutor().getEmail().equals(tutorEmail)) {
            throw new RuntimeException("Unauthorized: Not your enquiry");
        }

        enquiry.setStatus(response.getStatus());
        enquiry.setTutorResponse(response.getTutorResponse());

        if (response.getStatus() == SessionEnquiry.EnquiryStatus.APPROVED) {
            // Create 1:1 session
            TutoringSession session = createOneOnOneSession(enquiry);
            
            // Create Calendly event with student information
            createCalendlyEvent(session, enquiry.getStudent());
            
            // Send confirmation emails
            emailService.sendSessionConfirmation(enquiry.getStudent(), session);
            emailService.sendSessionConfirmation(enquiry.getTutor(), session);
        }

        SessionEnquiry savedEnquiry = sessionEnquiryRepository.save(enquiry);
        return sessionMapper.toDto(savedEnquiry);
    }

    // Tutoring Session Operations
    @Transactional
    public TutoringSessionDto createGroupSession(String tutorEmail, TutoringSessionCreateDto dto) {
        User tutor = userRepository.findByEmail(tutorEmail)
                .orElseThrow(() -> new RuntimeException("Tutor not found"));

        // Check tutor availability
        List<TutoringSession> conflictingSessions = tutoringSessionRepository
                .findByTutorAndTimeRange(tutor, dto.getStartTime(), dto.getEndTime());
        
        if (!conflictingSessions.isEmpty()) {
            throw new RuntimeException("You are not available for the requested time slot");
        }

        TutoringSession session = new TutoringSession();
        session.setTutor(tutor);
        session.setSubject(dto.getSubject());
        session.setTitle(dto.getTitle());
        session.setDescription(dto.getDescription());
        session.setStartTime(dto.getStartTime());
        session.setEndTime(dto.getEndTime());
        session.setSessionType(dto.getSessionType());
        session.setMaxStudents(dto.getMaxStudents());
        session.setStatus(TutoringSession.SessionStatus.SCHEDULED);

        TutoringSession savedSession = tutoringSessionRepository.save(session);
        
        // For group sessions, we'll create the Calendly event type but not schedule a specific event yet
        // Individual students will get scheduling links when they enroll
        log.info("Group session created - Calendly event type will be created when first student enrolls");
        
        return sessionMapper.toDto(savedSession);
    }

    @Transactional
    public TutoringSessionDto enrollInSession(String studentEmail, Long sessionId) {
        User student = userRepository.findByEmail(studentEmail)
                .orElseThrow(() -> new RuntimeException("Student not found"));

        TutoringSession session = tutoringSessionRepository.findById(sessionId)
                .orElseThrow(() -> new RuntimeException("Session not found"));

        if (session.getSessionType() != TutoringSession.SessionType.GROUP) {
            throw new RuntimeException("Can only enroll in group sessions");
        }

        if (session.getEnrolledStudents().size() >= session.getMaxStudents()) {
            throw new RuntimeException("Session is full");
        }

        if (session.getEnrolledStudents().contains(student)) {
            throw new RuntimeException("Already enrolled in this session");
        }

        session.getEnrolledStudents().add(student);
        TutoringSession savedSession = tutoringSessionRepository.save(session);
        
        // Send confirmation email
        emailService.sendSessionConfirmation(student, savedSession);
        
        return sessionMapper.toDto(savedSession);
    }

    // Query Operations
    public List<SessionEnquiryDto> getEnquiriesForTutor(String tutorEmail) {
        User tutor = userRepository.findByEmail(tutorEmail)
                .orElseThrow(() -> new RuntimeException("Tutor not found"));
        
        List<SessionEnquiry> enquiries = sessionEnquiryRepository.findByTutorOrderByCreatedAtDesc(tutor);
        return sessionMapper.toEnquiryDtos(enquiries);
    }

    public List<SessionEnquiryDto> getEnquiriesForStudent(String studentEmail) {
        User student = userRepository.findByEmail(studentEmail)
                .orElseThrow(() -> new RuntimeException("Student not found"));
        
        List<SessionEnquiry> enquiries = sessionEnquiryRepository.findByStudentOrderByCreatedAtDesc(student);
        return sessionMapper.toEnquiryDtos(enquiries);
    }

    public List<TutoringSessionDto> getSessionsForTutor(String tutorEmail) {
        User tutor = userRepository.findByEmail(tutorEmail)
                .orElseThrow(() -> new RuntimeException("Tutor not found"));
        
        List<TutoringSession> sessions = tutoringSessionRepository.findByTutorOrderByStartTimeDesc(tutor);
        return sessionMapper.toSessionDtos(sessions);
    }

    public List<TutoringSessionDto> getSessionsForStudent(String studentEmail) {
        User student = userRepository.findByEmail(studentEmail)
                .orElseThrow(() -> new RuntimeException("Student not found"));
        
        List<TutoringSession> sessions = tutoringSessionRepository.findByEnrolledStudentsContaining(student);
        return sessionMapper.toSessionDtos(sessions);
    }

    public List<TutoringSessionDto> getAvailableGroupSessions(User.Subject subject) {
        List<TutoringSession> sessions = tutoringSessionRepository
                .findAvailableGroupSessionsBySubject(subject, LocalDateTime.now());
        return sessionMapper.toSessionDtos(sessions);
    }

    public List<UserDto> getTutorsBySubject(User.Subject subject) {
        List<User> tutors = userRepository.findTutorsBySubject(subject);
        return tutors.stream()
                .map(tutor -> {
                    UserDto dto = new UserDto();
                    dto.setId(tutor.getId());
                    dto.setEmail(tutor.getEmail());
                    dto.setFirstName(tutor.getFirstName());
                    dto.setLastName(tutor.getLastName());
                    dto.setSubjects(tutor.getSubjects());
                    dto.setBio(tutor.getBio());
                    dto.setHourlyRate(tutor.getHourlyRate());
                    return dto;
                })
                .toList();
    }

    // Private helper methods
    private TutoringSession createOneOnOneSession(SessionEnquiry enquiry) {
        TutoringSession session = new TutoringSession();
        session.setTutor(enquiry.getTutor());
        session.setSubject(enquiry.getSubject());
        session.setTitle("1:1 Session - " + enquiry.getSubject().name());
        session.setDescription("One-on-one tutoring session");
        session.setStartTime(enquiry.getRequestedStartTime());
        session.setEndTime(enquiry.getRequestedEndTime());
        session.setSessionType(TutoringSession.SessionType.ONE_ON_ONE);
        session.setMaxStudents(1);
        session.setStatus(TutoringSession.SessionStatus.SCHEDULED);
        session.setEnrolledStudents(Set.of(enquiry.getStudent()));

        return tutoringSessionRepository.save(session);
    }

    private void createCalendlyEvent(TutoringSession session, User student) {
        try {
            log.info("Attempting to create Calendly event for session {} with student {}", session.getId(), student.getEmail());
            if (session.getTutor().getCalendlyUri() != null && session.getTutor().getCalendlyAccessToken() != null) {
                log.info("Tutor has Calendly connection configured for user: {}", session.getTutor().getCalendlyUri());

                // Create event type first, then create scheduled event
                createCalendlyEventTypeAndScheduleEvent(session, student);

            } else {
                log.info("Tutor does not have Calendly connection configured");
            }
        } catch (Exception e) {
            log.error("Failed to process Calendly integration for session {}: {}", session.getId(), e.getMessage());
        }
    }


    private void createCalendlyEventTypeAndScheduleEvent(TutoringSession session, User student) {
        try {
            String accessToken = session.getTutor().getCalendlyAccessToken();
            String tutorUri = session.getTutor().getCalendlyUri();

            log.info("Creating new event type for session {} with tutor: {}", session.getId(), tutorUri);

            // Step 1: Create a new event type for this specific tutoring session
            CalendlyService.CalendlyEventTypeCreateRequest eventTypeRequest = createEventTypeRequest(session, tutorUri);

            calendlyService.createEventType(accessToken, eventTypeRequest)
                    .flatMap(eventType -> {
                        log.info("Successfully created event type: {} with URI: {}", eventType.getName(), eventType.getUri());

                        // Step 2: Create a scheduling link for the student using the new event type
                        return calendlyService.createSchedulingLink(
                            eventType.getUri(),
                            student.getEmail(),
                            student.getFirstName() + " " + student.getLastName(),
                            accessToken
                        );
                    })
                    .doOnSuccess(schedulingLink -> {
                        if (schedulingLink != null) {
                            log.info("Successfully created Calendly scheduling link for session {}: {}",
                                    session.getId(), schedulingLink.getBooking_url());

                            // Update session with Calendly scheduling link
                            session.setCalendlyEventUri(schedulingLink.getBooking_url());
                            tutoringSessionRepository.save(session);

                            log.info("Student {} can now book the session using: {}", student.getEmail(), schedulingLink.getBooking_url());
                            log.info("When student books, Calendly will automatically send email notifications to both tutor and student");

                            // Send email to student with booking link
                            sendBookingLinkToStudent(student, session, schedulingLink.getBooking_url());
                        }
                    })
                    .doOnError(error -> {
                        log.error("Failed to create Calendly event type and scheduling link for session {}: {}", session.getId(), error.getMessage());
                        if (error instanceof org.springframework.web.reactive.function.client.WebClientResponseException) {
                            org.springframework.web.reactive.function.client.WebClientResponseException webError =
                                (org.springframework.web.reactive.function.client.WebClientResponseException) error;
                            log.error("HTTP Status: {}, Response: {}", webError.getStatusCode(), webError.getResponseBodyAsString());
                        }
                        error.printStackTrace();
                    })
                    .subscribe();

        } catch (Exception e) {
            log.error("Exception in createCalendlyEventTypeAndScheduleEvent for session {}: {}", session.getId(), e.getMessage());
        }
    }

    private CalendlyService.CalendlyEventTypeCreateRequest createEventTypeRequest(TutoringSession session, String tutorUri) {
        CalendlyService.CalendlyEventTypeCreateRequest request = new CalendlyService.CalendlyEventTypeCreateRequest();

        // Required fields
        request.setOwner(tutorUri);
        request.setName(session.getTitle() + " - " + session.getSubject());
        request.setDuration(calculateSessionDuration(session));

        // Optional fields
        request.setDescription("Tutoring session: " + session.getDescription());
        request.setActive(true);
        request.setColor("#0069ff"); // Calendly blue
        request.setLocale("en");

        // Add default location (ask invitee to choose)
        CalendlyService.CalendlyLocationConfiguration location = new CalendlyService.CalendlyLocationConfiguration();
        location.setKind("ask_invitee");
        request.setLocations(List.of(location));

        return request;
    }

    private Integer calculateSessionDuration(TutoringSession session) {
        // Calculate duration in minutes
        if (session.getStartTime() != null && session.getEndTime() != null) {
            return (int) java.time.Duration.between(session.getStartTime(), session.getEndTime()).toMinutes();
        }
        return 60; // Default to 60 minutes
    }

    private void sendBookingLinkToStudent(User student, TutoringSession session, String bookingUrl) {
        try {
            // Send email to student with the Calendly booking link
            String subject = "Book Your Tutoring Session - " + session.getTitle();
            String message = String.format(
                "Hi %s,\n\n" +
                "Your tutoring session has been approved!\n\n" +
                "Session Details:\n" +
                "- Subject: %s\n" +
                "- Title: %s\n" +
                "- Description: %s\n\n" +
                "Please click the link below to book your preferred time slot:\n" +
                "%s\n\n" +
                "Once you book, both you and your tutor will receive confirmation emails from Calendly.\n\n" +
                "Best regards,\n" +
                "Tutoring Platform Team",
                student.getFirstName(),
                session.getSubject(),
                session.getTitle(),
                session.getDescription(),
                bookingUrl
            );

            // Use existing email service to send the booking link
            emailService.sendEmail(student.getEmail(), subject, message);
            log.info("Sent booking link email to student: {}", student.getEmail());

        } catch (Exception e) {
            log.error("Failed to send booking link email to student {}: {}", student.getEmail(), e.getMessage());
        }
    }

}
