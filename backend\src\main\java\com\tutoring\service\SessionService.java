package com.tutoring.service;

import com.tutoring.dto.*;
import com.tutoring.entity.*;
import com.tutoring.mapper.*;
import com.tutoring.repository.*;
import jakarta.transaction.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Service
@RequiredArgsConstructor
@Slf4j
public class SessionService {

    private final SessionEnquiryRepository sessionEnquiryRepository;
    private final TutoringSessionRepository tutoringSessionRepository;
    private final UserRepository userRepository;
    private final SessionMapper sessionMapper;
    private final CalendlyService calendlyService;
    private final EmailService emailService;

    // Session Enquiry Operations
    @Transactional
    public SessionEnquiryDto createSessionEnquiry(String studentEmail, SessionEnquiryCreateDto dto) {
        User student = userRepository.findByEmail(studentEmail)
                .orElseThrow(() -> new RuntimeException("Student not found"));
        
        User tutor = userRepository.findById(dto.getTutorId())
                .orElseThrow(() -> new RuntimeException("Tutor not found"));

        // Check if tutor is available for the requested time
        List<TutoringSession> conflictingSessions = tutoringSessionRepository
                .findByTutorAndTimeRange(tutor, dto.getRequestedStartTime(), dto.getRequestedEndTime());
        
        if (!conflictingSessions.isEmpty()) {
            throw new RuntimeException("Tutor is not available for the requested time slot");
        }

        SessionEnquiry enquiry = new SessionEnquiry();
        enquiry.setStudent(student);
        enquiry.setTutor(tutor);
        enquiry.setSubject(dto.getSubject());
        enquiry.setRequestedStartTime(dto.getRequestedStartTime());
        enquiry.setRequestedEndTime(dto.getRequestedEndTime());
        enquiry.setMessage(dto.getMessage());
        enquiry.setStatus(SessionEnquiry.EnquiryStatus.PENDING);

        SessionEnquiry savedEnquiry = sessionEnquiryRepository.save(enquiry);
        
        // Send notification email to tutor
        emailService.sendEnquiryNotification(tutor, savedEnquiry);
        
        return sessionMapper.toDto(savedEnquiry);
    }

    @Transactional
    public SessionEnquiryDto respondToEnquiry(Long enquiryId, String tutorEmail, SessionEnquiryResponseDto response) {
        SessionEnquiry enquiry = sessionEnquiryRepository.findById(enquiryId)
                .orElseThrow(() -> new RuntimeException("Enquiry not found"));

        if (!enquiry.getTutor().getEmail().equals(tutorEmail)) {
            throw new RuntimeException("Unauthorized: Not your enquiry");
        }

        enquiry.setStatus(response.getStatus());
        enquiry.setTutorResponse(response.getTutorResponse());

        if (response.getStatus() == SessionEnquiry.EnquiryStatus.APPROVED) {
            // Create 1:1 session
            TutoringSession session = createOneOnOneSession(enquiry);
            
            // Create Calendly event
            createCalendlyEvent(session);
            
            // Send confirmation emails
            emailService.sendSessionConfirmation(enquiry.getStudent(), session);
            emailService.sendSessionConfirmation(enquiry.getTutor(), session);
        }

        SessionEnquiry savedEnquiry = sessionEnquiryRepository.save(enquiry);
        return sessionMapper.toDto(savedEnquiry);
    }

    // Tutoring Session Operations
    @Transactional
    public TutoringSessionDto createGroupSession(String tutorEmail, TutoringSessionCreateDto dto) {
        User tutor = userRepository.findByEmail(tutorEmail)
                .orElseThrow(() -> new RuntimeException("Tutor not found"));

        // Check tutor availability
        List<TutoringSession> conflictingSessions = tutoringSessionRepository
                .findByTutorAndTimeRange(tutor, dto.getStartTime(), dto.getEndTime());
        
        if (!conflictingSessions.isEmpty()) {
            throw new RuntimeException("You are not available for the requested time slot");
        }

        TutoringSession session = new TutoringSession();
        session.setTutor(tutor);
        session.setSubject(dto.getSubject());
        session.setTitle(dto.getTitle());
        session.setDescription(dto.getDescription());
        session.setStartTime(dto.getStartTime());
        session.setEndTime(dto.getEndTime());
        session.setSessionType(dto.getSessionType());
        session.setMaxStudents(dto.getMaxStudents());
        session.setStatus(TutoringSession.SessionStatus.SCHEDULED);

        TutoringSession savedSession = tutoringSessionRepository.save(session);
        
        // Create Calendly event
        createCalendlyEvent(savedSession);
        
        return sessionMapper.toDto(savedSession);
    }

    @Transactional
    public TutoringSessionDto enrollInSession(String studentEmail, Long sessionId) {
        User student = userRepository.findByEmail(studentEmail)
                .orElseThrow(() -> new RuntimeException("Student not found"));

        TutoringSession session = tutoringSessionRepository.findById(sessionId)
                .orElseThrow(() -> new RuntimeException("Session not found"));

        if (session.getSessionType() != TutoringSession.SessionType.GROUP) {
            throw new RuntimeException("Can only enroll in group sessions");
        }

        if (session.getEnrolledStudents().size() >= session.getMaxStudents()) {
            throw new RuntimeException("Session is full");
        }

        if (session.getEnrolledStudents().contains(student)) {
            throw new RuntimeException("Already enrolled in this session");
        }

        session.getEnrolledStudents().add(student);
        TutoringSession savedSession = tutoringSessionRepository.save(session);
        
        // Send confirmation email
        emailService.sendSessionConfirmation(student, savedSession);
        
        return sessionMapper.toDto(savedSession);
    }

    // Query Operations
    public List<SessionEnquiryDto> getEnquiriesForTutor(String tutorEmail) {
        User tutor = userRepository.findByEmail(tutorEmail)
                .orElseThrow(() -> new RuntimeException("Tutor not found"));
        
        List<SessionEnquiry> enquiries = sessionEnquiryRepository.findByTutorOrderByCreatedAtDesc(tutor);
        return sessionMapper.toEnquiryDtos(enquiries);
    }

    public List<SessionEnquiryDto> getEnquiriesForStudent(String studentEmail) {
        User student = userRepository.findByEmail(studentEmail)
                .orElseThrow(() -> new RuntimeException("Student not found"));
        
        List<SessionEnquiry> enquiries = sessionEnquiryRepository.findByStudentOrderByCreatedAtDesc(student);
        return sessionMapper.toEnquiryDtos(enquiries);
    }

    public List<TutoringSessionDto> getSessionsForTutor(String tutorEmail) {
        User tutor = userRepository.findByEmail(tutorEmail)
                .orElseThrow(() -> new RuntimeException("Tutor not found"));
        
        List<TutoringSession> sessions = tutoringSessionRepository.findByTutorOrderByStartTimeDesc(tutor);
        return sessionMapper.toSessionDtos(sessions);
    }

    public List<TutoringSessionDto> getSessionsForStudent(String studentEmail) {
        User student = userRepository.findByEmail(studentEmail)
                .orElseThrow(() -> new RuntimeException("Student not found"));
        
        List<TutoringSession> sessions = tutoringSessionRepository.findByEnrolledStudentsContaining(student);
        return sessionMapper.toSessionDtos(sessions);
    }

    public List<TutoringSessionDto> getAvailableGroupSessions(User.Subject subject) {
        List<TutoringSession> sessions = tutoringSessionRepository
                .findAvailableGroupSessionsBySubject(subject, LocalDateTime.now());
        return sessionMapper.toSessionDtos(sessions);
    }

    public List<UserDto> getTutorsBySubject(User.Subject subject) {
        List<User> tutors = userRepository.findTutorsBySubject(subject);
        return tutors.stream()
                .map(tutor -> {
                    UserDto dto = new UserDto();
                    dto.setId(tutor.getId());
                    dto.setEmail(tutor.getEmail());
                    dto.setFirstName(tutor.getFirstName());
                    dto.setLastName(tutor.getLastName());
                    dto.setSubjects(tutor.getSubjects());
                    dto.setBio(tutor.getBio());
                    dto.setHourlyRate(tutor.getHourlyRate());
                    return dto;
                })
                .toList();
    }

    // Private helper methods
    private TutoringSession createOneOnOneSession(SessionEnquiry enquiry) {
        TutoringSession session = new TutoringSession();
        session.setTutor(enquiry.getTutor());
        session.setSubject(enquiry.getSubject());
        session.setTitle("1:1 Session - " + enquiry.getSubject().name());
        session.setDescription("One-on-one tutoring session");
        session.setStartTime(enquiry.getRequestedStartTime());
        session.setEndTime(enquiry.getRequestedEndTime());
        session.setSessionType(TutoringSession.SessionType.ONE_ON_ONE);
        session.setMaxStudents(1);
        session.setStatus(TutoringSession.SessionStatus.SCHEDULED);
        session.setEnrolledStudents(Set.of(enquiry.getStudent()));

        return tutoringSessionRepository.save(session);
    }

    private void createCalendlyEvent(TutoringSession session) {
        try {
            log.info("Attempting to create Calendly event for session {}", session.getId());
            if (session.getTutor().getCalendlyUri() != null && session.getTutor().getCalendlyAccessToken() != null) {
                log.info("Tutor has Calendly connection configured for user: {}", session.getTutor().getCalendlyUri());

                // For now, we'll just log that Calendly integration is available
                // The actual scheduling link creation will be handled separately
                // This avoids the reactive programming complexity in the synchronous transaction
                log.info("Calendly integration available for session {}. Scheduling link can be created when needed.", session.getId());

                // Set a placeholder that indicates Calendly is available
                session.setCalendlyEventUri("calendly_available");
                tutoringSessionRepository.save(session);

                // TODO: Implement async Calendly scheduling link creation
                // This could be done via:
                // 1. A separate async service
                // 2. An event-driven approach
                // 3. On-demand when student requests booking

            } else {
                log.info("Tutor does not have Calendly connection configured");
            }
        } catch (Exception e) {
            log.error("Failed to process Calendly integration for session {}: {}", session.getId(), e.getMessage());
        }
    }

}
