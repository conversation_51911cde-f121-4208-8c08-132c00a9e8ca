package com.tutoring.mapper;

import com.tutoring.dto.UserDto;
import com.tutoring.dto.UserRegistrationDto;
import com.tutoring.entity.User;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-27T15:43:25+0530",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.6 (Oracle Corporation)"
)
@Component
public class UserMapperImpl implements UserMapper {

    @Override
    public UserDto toDto(User user) {
        if ( user == null ) {
            return null;
        }

        UserDto userDto = new UserDto();

        userDto.setId( user.getId() );
        userDto.setEmail( user.getEmail() );
        userDto.setFirstName( user.getFirstName() );
        userDto.setLastName( user.getLastName() );
        userDto.setPhone( user.getPhone() );
        userDto.setRole( user.getRole() );
        userDto.setCalendlyUri( user.getCalendlyUri() );
        userDto.setCreatedAt( user.getCreatedAt() );
        userDto.setUpdatedAt( user.getUpdatedAt() );
        userDto.setActive( user.getActive() );
        Set<User.Subject> set = user.getSubjects();
        if ( set != null ) {
            userDto.setSubjects( new LinkedHashSet<User.Subject>( set ) );
        }
        userDto.setBio( user.getBio() );
        userDto.setHourlyRate( user.getHourlyRate() );

        return userDto;
    }

    @Override
    public List<UserDto> toDtos(List<User> users) {
        if ( users == null ) {
            return null;
        }

        List<UserDto> list = new ArrayList<UserDto>( users.size() );
        for ( User user : users ) {
            list.add( toDto( user ) );
        }

        return list;
    }

    @Override
    public User toEntity(UserDto dto) {
        if ( dto == null ) {
            return null;
        }

        User user = new User();

        user.setEmail( dto.getEmail() );
        user.setFirstName( dto.getFirstName() );
        user.setLastName( dto.getLastName() );
        user.setPhone( dto.getPhone() );
        user.setRole( dto.getRole() );
        Set<User.Subject> set = dto.getSubjects();
        if ( set != null ) {
            user.setSubjects( new LinkedHashSet<User.Subject>( set ) );
        }
        user.setBio( dto.getBio() );
        user.setHourlyRate( dto.getHourlyRate() );

        return user;
    }

    @Override
    public void updateEntity(User entity, UserDto dto) {
        if ( dto == null ) {
            return;
        }

        entity.setEmail( dto.getEmail() );
        entity.setFirstName( dto.getFirstName() );
        entity.setLastName( dto.getLastName() );
        entity.setPhone( dto.getPhone() );
        entity.setRole( dto.getRole() );
        if ( entity.getSubjects() != null ) {
            Set<User.Subject> set = dto.getSubjects();
            if ( set != null ) {
                entity.getSubjects().clear();
                entity.getSubjects().addAll( set );
            }
            else {
                entity.setSubjects( null );
            }
        }
        else {
            Set<User.Subject> set = dto.getSubjects();
            if ( set != null ) {
                entity.setSubjects( new LinkedHashSet<User.Subject>( set ) );
            }
        }
        entity.setBio( dto.getBio() );
        entity.setHourlyRate( dto.getHourlyRate() );
    }

    @Override
    public User fromRegistrationDto(UserRegistrationDto dto) {
        if ( dto == null ) {
            return null;
        }

        User user = new User();

        user.setEmail( dto.getEmail() );
        user.setPassword( dto.getPassword() );
        user.setFirstName( dto.getFirstName() );
        user.setLastName( dto.getLastName() );
        user.setPhone( dto.getPhone() );
        user.setRole( dto.getRole() );
        Set<User.Subject> set = dto.getSubjects();
        if ( set != null ) {
            user.setSubjects( new LinkedHashSet<User.Subject>( set ) );
        }
        user.setBio( dto.getBio() );
        user.setHourlyRate( dto.getHourlyRate() );

        return user;
    }
}
