package com.tutoring.service;

import com.tutoring.entity.SessionEnquiry;
import com.tutoring.entity.TutoringSession;
import com.tutoring.entity.User;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;

@Service
@RequiredArgsConstructor
@Slf4j
public class EmailService {

    private final JavaMailSender mailSender;

    @Async
    public void sendEnquiryNotification(User tutor, SessionEnquiry enquiry) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(tutor.getEmail());
            message.setSubject("New Session Enquiry - " + enquiry.getSubject().name());
            
            String body = String.format(
                "Dear %s,\n\n" +
                "You have received a new session enquiry from %s %s.\n\n" +
                "Subject: %s\n" +
                "Requested Time: %s to %s\n" +
                "Message: %s\n\n" +
                "Please log in to your dashboard to respond to this enquiry.\n\n" +
                "Best regards,\n" +
                "Tutoring Platform Team",
                tutor.getFirstName(),
                enquiry.getStudent().getFirstName(),
                enquiry.getStudent().getLastName(),
                enquiry.getSubject().name(),
                enquiry.getRequestedStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
                enquiry.getRequestedEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
                enquiry.getMessage() != null ? enquiry.getMessage() : "No message provided"
            );
            
            message.setText(body);
            mailSender.send(message);
            
            log.info("Enquiry notification email sent to tutor: {}", tutor.getEmail());
        } catch (Exception e) {
            log.error("Failed to send enquiry notification email to {}: {}", tutor.getEmail(), e.getMessage());
        }
    }

    @Async
    public void sendSessionConfirmation(User user, TutoringSession session) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(user.getEmail());
            message.setSubject("Session Confirmation - " + session.getTitle());
            
            String body = String.format(
                "Dear %s,\n\n" +
                "Your tutoring session has been confirmed.\n\n" +
                "Session Details:\n" +
                "Title: %s\n" +
                "Subject: %s\n" +
                "Tutor: %s %s\n" +
                "Date & Time: %s to %s\n" +
                "Type: %s\n\n" +
                "%s\n\n" +
                "Please add this session to your calendar and be on time.\n\n" +
                "Best regards,\n" +
                "Tutoring Platform Team",
                user.getFirstName(),
                session.getTitle(),
                session.getSubject().name(),
                session.getTutor().getFirstName(),
                session.getTutor().getLastName(),
                session.getStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
                session.getEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
                session.getSessionType().name(),
                session.getDescription() != null ? "Description: " + session.getDescription() : ""
            );
            
            message.setText(body);
            mailSender.send(message);
            
            log.info("Session confirmation email sent to: {}", user.getEmail());
        } catch (Exception e) {
            log.error("Failed to send session confirmation email to {}: {}", user.getEmail(), e.getMessage());
        }
    }

    @Async
    public void sendEnquiryResponse(User student, SessionEnquiry enquiry) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(student.getEmail());
            
            String status = enquiry.getStatus() == SessionEnquiry.EnquiryStatus.APPROVED ? "Approved" : "Rejected";
            message.setSubject("Session Enquiry " + status + " - " + enquiry.getSubject().name());
            
            String body = String.format(
                "Dear %s,\n\n" +
                "Your session enquiry has been %s by %s %s.\n\n" +
                "Subject: %s\n" +
                "Requested Time: %s to %s\n" +
                "Tutor Response: %s\n\n" +
                "%s\n\n" +
                "Best regards,\n" +
                "Tutoring Platform Team",
                student.getFirstName(),
                status.toLowerCase(),
                enquiry.getTutor().getFirstName(),
                enquiry.getTutor().getLastName(),
                enquiry.getSubject().name(),
                enquiry.getRequestedStartTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
                enquiry.getRequestedEndTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
                enquiry.getTutorResponse() != null ? enquiry.getTutorResponse() : "No additional message",
                enquiry.getStatus() == SessionEnquiry.EnquiryStatus.APPROVED ? 
                    "A session has been scheduled and you will receive a confirmation email shortly." :
                    "Please feel free to submit another enquiry with different time slots."
            );
            
            message.setText(body);
            mailSender.send(message);
            
            log.info("Enquiry response email sent to: {}", student.getEmail());
        } catch (Exception e) {
            log.error("Failed to send enquiry response email to {}: {}", student.getEmail(), e.getMessage());
        }
    }

    @Async
    public void sendWelcomeEmail(User user) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(user.getEmail());
            message.setSubject("Welcome to Tutoring Platform!");
            
            String body = String.format(
                "Dear %s,\n\n" +
                "Welcome to our Tutoring Platform! Your account has been successfully created.\n\n" +
                "Account Details:\n" +
                "Name: %s %s\n" +
                "Email: %s\n" +
                "Role: %s\n\n" +
                "%s\n\n" +
                "You can now log in and start using the platform.\n\n" +
                "Best regards,\n" +
                "Tutoring Platform Team",
                user.getFirstName(),
                user.getFirstName(),
                user.getLastName(),
                user.getEmail(),
                user.getRole().name(),
                user.getRole() == User.UserRole.TUTOR ? 
                    "As a tutor, you can set up your availability, respond to session enquiries, and create group sessions." :
                    "As a student, you can search for tutors, request sessions, and enroll in group sessions."
            );
            
            message.setText(body);
            mailSender.send(message);
            
            log.info("Welcome email sent to: {}", user.getEmail());
        } catch (Exception e) {
            log.error("Failed to send welcome email to {}: {}", user.getEmail(), e.getMessage());
        }
    }

    @Async
    public void sendEmail(String to, String subject, String body) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setTo(to);
            message.setSubject(subject);
            message.setText(body);
            mailSender.send(message);

            log.info("Email sent to: {}", to);
        } catch (Exception e) {
            log.error("Failed to send email to {}: {}", to, e.getMessage());
        }
    }
}
