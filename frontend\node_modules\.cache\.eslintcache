[{"C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\contexts\\AuthContext.js": "3", "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\components\\Navbar.js": "4", "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\pages\\Login.js": "5", "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\pages\\Register.js": "6", "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\pages\\StudentDashboard.js": "7", "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\pages\\GroupSessions.js": "8", "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\pages\\Profile.js": "9", "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\pages\\TutorDashboard.js": "10", "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\pages\\SearchTutors.js": "11", "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\services\\api.js": "12"}, {"size": 265, "mtime": 1750998539386, "results": "13", "hashOfConfig": "14"}, {"size": 2720, "mtime": 1750998539302, "results": "15", "hashOfConfig": "14"}, {"size": 1940, "mtime": 1750998539508, "results": "16", "hashOfConfig": "14"}, {"size": 1558, "mtime": 1750998539449, "results": "17", "hashOfConfig": "14"}, {"size": 2962, "mtime": 1750998539623, "results": "18", "hashOfConfig": "14"}, {"size": 7207, "mtime": 1750998539717, "results": "19", "hashOfConfig": "14"}, {"size": 8289, "mtime": 1750998539791, "results": "20", "hashOfConfig": "14"}, {"size": 3376, "mtime": 1750998539575, "results": "21", "hashOfConfig": "14"}, {"size": 10732, "mtime": 1751018347254, "results": "22", "hashOfConfig": "14"}, {"size": 16744, "mtime": 1750998539840, "results": "23", "hashOfConfig": "14"}, {"size": 8975, "mtime": 1750998539761, "results": "24", "hashOfConfig": "14"}, {"size": 861, "mtime": 1750998539887, "results": "25", "hashOfConfig": "14"}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qsxree", {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\pages\\Register.js", [], [], "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\pages\\StudentDashboard.js", ["62", "63", "64", "65"], [], "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\pages\\GroupSessions.js", ["66"], [], "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\pages\\Profile.js", [], [], "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\pages\\TutorDashboard.js", ["67"], [], "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\pages\\SearchTutors.js", ["68"], [], "C:\\Users\\<USER>\\Downloads\\POC\\frontend\\src\\services\\api.js", [], [], {"ruleId": "69", "severity": 1, "message": "70", "line": 8, "column": 3, "nodeType": "71", "messageId": "72", "endLine": 8, "endColumn": 14}, {"ruleId": "69", "severity": 1, "message": "73", "line": 9, "column": 3, "nodeType": "71", "messageId": "72", "endLine": 9, "endColumn": 9}, {"ruleId": "69", "severity": 1, "message": "74", "line": 18, "column": 3, "nodeType": "71", "messageId": "72", "endLine": 18, "endColumn": 8}, {"ruleId": "75", "severity": 1, "message": "76", "line": 34, "column": 6, "nodeType": "77", "endLine": 34, "endColumn": 8, "suggestions": "78"}, {"ruleId": "69", "severity": 1, "message": "79", "line": 11, "column": 3, "nodeType": "71", "messageId": "72", "endLine": 11, "endColumn": 7}, {"ruleId": "75", "severity": 1, "message": "76", "line": 59, "column": 6, "nodeType": "77", "endLine": 59, "endColumn": 8, "suggestions": "80"}, {"ruleId": "75", "severity": 1, "message": "81", "line": 98, "column": 6, "nodeType": "77", "endLine": 98, "endColumn": 23, "suggestions": "82"}, "no-unused-vars", "'CardActions' is defined but never used.", "Identifier", "unusedVar", "'Button' is defined but never used.", "'Paper' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", "ArrayExpression", ["83"], "'Chip' is defined but never used.", ["84"], "React Hook useEffect has a missing dependency: 'searchTutors'. Either include it or remove the dependency array.", ["85"], {"desc": "86", "fix": "87"}, {"desc": "86", "fix": "88"}, {"desc": "89", "fix": "90"}, "Update the dependencies array to be: [fetchData]", {"range": "91", "text": "92"}, {"range": "93", "text": "92"}, "Update the dependencies array to be: [searchTutors, selectedSubject]", {"range": "94", "text": "95"}, [700, 702], "[fetchData]", [1440, 1442], [2898, 2915], "[searchTutors, selectedSubject]"]