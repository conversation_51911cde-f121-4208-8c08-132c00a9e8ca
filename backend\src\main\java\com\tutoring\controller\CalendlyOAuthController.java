package com.tutoring.controller;

import com.tutoring.service.CalendlyOAuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.view.RedirectView;

@RestController
@RequestMapping("/api/auth/calendly")
@RequiredArgsConstructor
@CrossOrigin(origins = "http://localhost:3000")
@Slf4j
public class CalendlyOAuthController {

    private final CalendlyOAuthService calendlyOAuthService;

    @Value("${calendly.oauth.client-id}")
    private String clientId;

    @Value("${calendly.oauth.redirect-uri}")
    private String redirectUri;

    @Value("${calendly.oauth.mock-mode:true}")
    private boolean mockMode;

    @GetMapping("/authorize")
    public ResponseEntity<String> getAuthorizationUrl(@RequestParam String userEmail) {
        log.info("Calendly authorization request for user: {}, mockMode: {}", userEmail, mockMode);

        if (mockMode) {
            // In mock mode, simulate the OAuth flow by directly calling the callback
            log.info("Mock mode enabled - simulating Calendly OAuth for user: {}", userEmail);
            try {
                calendlyOAuthService.simulateCalendlyConnection(userEmail);
                String successUrl = "http://localhost:3000/profile?success=calendly_connected";
                return ResponseEntity.ok(successUrl);
            } catch (Exception e) {
                log.error("Error in mock Calendly connection for user: {}", userEmail, e);
                String errorUrl = "http://localhost:3000/profile?error=calendly_connection_failed";
                return ResponseEntity.ok(errorUrl);
            }
        }

        String authUrl = String.format(
            "https://auth.calendly.com/oauth/authorize?client_id=%s&response_type=code&redirect_uri=%s&state=%s&scope=%s",
            clientId,
            redirectUri,
            userEmail, // Using email as state to identify user
            "default" // Calendly default scope
        );

        log.info("Generated Calendly OAuth URL for user {}: {}", userEmail, authUrl);
        return ResponseEntity.ok(authUrl);
    }

    @GetMapping("/callback")
    public RedirectView handleCallback(
            @RequestParam String code,
            @RequestParam String state, // This will be the user email
            @RequestParam(required = false) String error) {
        
        if (error != null) {
            log.error("Calendly OAuth error: {}", error);
            return new RedirectView("http://localhost:3000/profile?error=calendly_auth_failed");
        }
        System.out.println("Calendly Code: "+ code);
        try {
            // Exchange code for access token and update user
            calendlyOAuthService.exchangeCodeForToken(code, state);
            return new RedirectView("http://localhost:3000/profile?success=calendly_connected");
        } catch (Exception e) {
            log.error("Error processing Calendly callback", e);
            return new RedirectView("http://localhost:3000/profile?error=calendly_connection_failed");
        }
    }

    @PostMapping("/disconnect")
    public ResponseEntity<String> disconnectCalendly(@RequestParam String userEmail) {
        try {
            calendlyOAuthService.disconnectUser(userEmail);
            return ResponseEntity.ok("Calendly disconnected successfully");
        } catch (Exception e) {
            log.error("Error disconnecting Calendly for user: {}", userEmail, e);
            return ResponseEntity.badRequest().body("Failed to disconnect Calendly");
        }
    }
}
