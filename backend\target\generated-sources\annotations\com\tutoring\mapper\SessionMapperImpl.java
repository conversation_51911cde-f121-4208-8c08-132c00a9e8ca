package com.tutoring.mapper;

import com.tutoring.dto.SessionEnquiryDto;
import com.tutoring.dto.TutoringSessionDto;
import com.tutoring.dto.UserDto;
import com.tutoring.entity.SessionEnquiry;
import com.tutoring.entity.TutoringSession;
import com.tutoring.entity.User;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import javax.annotation.processing.Generated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-27T15:43:24+0530",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 21.0.6 (Oracle Corporation)"
)
@Component
public class SessionMapperImpl implements SessionMapper {

    @Autowired
    private UserMapper userMapper;

    @Override
    public SessionEnquiryDto toDto(SessionEnquiry enquiry) {
        if ( enquiry == null ) {
            return null;
        }

        SessionEnquiryDto sessionEnquiryDto = new SessionEnquiryDto();

        sessionEnquiryDto.setId( enquiry.getId() );
        sessionEnquiryDto.setStudent( userMapper.toDto( enquiry.getStudent() ) );
        sessionEnquiryDto.setTutor( userMapper.toDto( enquiry.getTutor() ) );
        sessionEnquiryDto.setSubject( enquiry.getSubject() );
        sessionEnquiryDto.setRequestedStartTime( enquiry.getRequestedStartTime() );
        sessionEnquiryDto.setRequestedEndTime( enquiry.getRequestedEndTime() );
        sessionEnquiryDto.setMessage( enquiry.getMessage() );
        sessionEnquiryDto.setStatus( enquiry.getStatus() );
        sessionEnquiryDto.setTutorResponse( enquiry.getTutorResponse() );
        sessionEnquiryDto.setCreatedAt( enquiry.getCreatedAt() );
        sessionEnquiryDto.setUpdatedAt( enquiry.getUpdatedAt() );

        return sessionEnquiryDto;
    }

    @Override
    public List<SessionEnquiryDto> toEnquiryDtos(List<SessionEnquiry> enquiries) {
        if ( enquiries == null ) {
            return null;
        }

        List<SessionEnquiryDto> list = new ArrayList<SessionEnquiryDto>( enquiries.size() );
        for ( SessionEnquiry sessionEnquiry : enquiries ) {
            list.add( toDto( sessionEnquiry ) );
        }

        return list;
    }

    @Override
    public SessionEnquiry toEntity(SessionEnquiryDto dto) {
        if ( dto == null ) {
            return null;
        }

        SessionEnquiry sessionEnquiry = new SessionEnquiry();

        sessionEnquiry.setStudent( userMapper.toEntity( dto.getStudent() ) );
        sessionEnquiry.setTutor( userMapper.toEntity( dto.getTutor() ) );
        sessionEnquiry.setSubject( dto.getSubject() );
        sessionEnquiry.setRequestedStartTime( dto.getRequestedStartTime() );
        sessionEnquiry.setRequestedEndTime( dto.getRequestedEndTime() );
        sessionEnquiry.setMessage( dto.getMessage() );

        return sessionEnquiry;
    }

    @Override
    public TutoringSessionDto toDto(TutoringSession session) {
        if ( session == null ) {
            return null;
        }

        TutoringSessionDto tutoringSessionDto = new TutoringSessionDto();

        tutoringSessionDto.setId( session.getId() );
        tutoringSessionDto.setTutor( userMapper.toDto( session.getTutor() ) );
        tutoringSessionDto.setSubject( session.getSubject() );
        tutoringSessionDto.setTitle( session.getTitle() );
        tutoringSessionDto.setDescription( session.getDescription() );
        tutoringSessionDto.setStartTime( session.getStartTime() );
        tutoringSessionDto.setEndTime( session.getEndTime() );
        tutoringSessionDto.setSessionType( session.getSessionType() );
        tutoringSessionDto.setMaxStudents( session.getMaxStudents() );
        tutoringSessionDto.setEnrolledStudents( userSetToUserDtoSet( session.getEnrolledStudents() ) );
        tutoringSessionDto.setCalendlyEventUri( session.getCalendlyEventUri() );
        tutoringSessionDto.setCalendlyEventId( session.getCalendlyEventId() );
        tutoringSessionDto.setStatus( session.getStatus() );
        tutoringSessionDto.setCreatedAt( session.getCreatedAt() );
        tutoringSessionDto.setUpdatedAt( session.getUpdatedAt() );

        return tutoringSessionDto;
    }

    @Override
    public List<TutoringSessionDto> toSessionDtos(List<TutoringSession> sessions) {
        if ( sessions == null ) {
            return null;
        }

        List<TutoringSessionDto> list = new ArrayList<TutoringSessionDto>( sessions.size() );
        for ( TutoringSession tutoringSession : sessions ) {
            list.add( toDto( tutoringSession ) );
        }

        return list;
    }

    @Override
    public TutoringSession toEntity(TutoringSessionDto dto) {
        if ( dto == null ) {
            return null;
        }

        TutoringSession tutoringSession = new TutoringSession();

        tutoringSession.setTutor( userMapper.toEntity( dto.getTutor() ) );
        tutoringSession.setSubject( dto.getSubject() );
        tutoringSession.setTitle( dto.getTitle() );
        tutoringSession.setDescription( dto.getDescription() );
        tutoringSession.setStartTime( dto.getStartTime() );
        tutoringSession.setEndTime( dto.getEndTime() );
        tutoringSession.setSessionType( dto.getSessionType() );
        tutoringSession.setMaxStudents( dto.getMaxStudents() );

        return tutoringSession;
    }

    protected Set<UserDto> userSetToUserDtoSet(Set<User> set) {
        if ( set == null ) {
            return null;
        }

        Set<UserDto> set1 = new LinkedHashSet<UserDto>( Math.max( (int) ( set.size() / .75f ) + 1, 16 ) );
        for ( User user : set ) {
            set1.add( userMapper.toDto( user ) );
        }

        return set1;
    }
}
