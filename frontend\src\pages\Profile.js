import React, { useState, useEffect } from 'react';
import {
  Con<PERSON><PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  TextField,
  Button,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  OutlinedInput,
  Chip,
  Alert
} from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import api from '../services/api';

const subjects = [
  'MATHEMATICS', 'PHYSICS', 'CHEMISTRY', 'BIOLOGY', 'ENGLISH',
  'HISTORY', 'GEOGRAPHY', 'COMPUTER_SCIENCE', 'ECONOMICS', 'PSYCHOLOGY'
];

function Profile() {
  const { user, updateUser } = useAuth();
  const [formData, setFormData] = useState({
    firstName: user?.firstName || '',
    lastName: user?.lastName || '',
    phone: user?.phone || '',
    subjects: user?.subjects || [],
    bio: user?.bio || '',
    hourlyRate: user?.hourlyRate || ''
  });
  const [loading, setLoading] = useState(false);
  const [alert, setAlert] = useState({ show: false, message: '', severity: 'success' });

  // Handle OAuth callback parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const success = urlParams.get('success');
    const error = urlParams.get('error');

    if (success === 'calendly_connected') {
      setAlert({ show: true, message: 'Calendly connected successfully!', severity: 'success' });
      // Clean up URL parameters
      window.history.replaceState({}, document.title, window.location.pathname);
    } else if (error) {
      let errorMessage = 'Failed to connect Calendly';
      if (error === 'calendly_auth_failed') {
        errorMessage = 'Calendly authorization failed';
      } else if (error === 'calendly_connection_failed') {
        errorMessage = 'Failed to establish Calendly connection';
      }
      setAlert({ show: true, message: errorMessage, severity: 'error' });
      // Clean up URL parameters
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubjectChange = (e) => {
    const { value } = e.target;
    setFormData(prev => ({ ...prev, subjects: typeof value === 'string' ? value.split(',') : value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await api.put(`/api/users/profile/${user.email}`, formData);
      updateUser(response.data);
      setAlert({ show: true, message: 'Profile updated successfully!', severity: 'success' });
    } catch (error) {
      console.error('Error updating profile:', error);
      setAlert({ show: true, message: 'Error updating profile', severity: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleCalendlyConfig = async () => {
    try {
      const response = await api.get(`/api/auth/calendly/authorize?userEmail=${user.email}`);
      // Redirect to Calendly OAuth URL
      window.location.href = response.data;
    } catch (error) {
      console.error('Error getting Calendly authorization URL:', error);
      setAlert({ show: true, message: 'Error connecting to Calendly', severity: 'error' });
    }
  };

  return (
    <Container maxWidth="md">
      <Box sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Profile Settings
        </Typography>

        {alert.show && (
          <Alert 
            severity={alert.severity} 
            sx={{ mb: 3 }}
            onClose={() => setAlert({ show: false, message: '', severity: 'success' })}
          >
            {alert.message}
          </Alert>
        )}

        <Card>
          <CardContent>
            <Box component="form" onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="First Name"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleChange}
                    required
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Last Name"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleChange}
                    required
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Email"
                    value={user?.email || ''}
                    disabled
                    helperText="Email cannot be changed"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Phone Number"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Role"
                    value={user?.role || ''}
                    disabled
                    helperText="Role cannot be changed"
                  />
                </Grid>

                {user?.role === 'TUTOR' && (
                  <>
                    <Grid item xs={12}>
                      <FormControl fullWidth>
                        <InputLabel>Subjects</InputLabel>
                        <Select
                          multiple
                          name="subjects"
                          value={formData.subjects}
                          onChange={handleSubjectChange}
                          input={<OutlinedInput label="Subjects" />}
                          renderValue={(selected) => (
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                              {selected.map((value) => (
                                <Chip key={value} label={value.replace('_', ' ')} />
                              ))}
                            </Box>
                          )}
                        >
                          {subjects.map((subject) => (
                            <MenuItem key={subject} value={subject}>
                              {subject.replace('_', ' ')}
                            </MenuItem>
                          ))}
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Bio"
                        name="bio"
                        multiline
                        rows={4}
                        value={formData.bio}
                        onChange={handleChange}
                        placeholder="Tell students about your teaching experience and approach..."
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        label="Hourly Rate ($)"
                        name="hourlyRate"
                        type="number"
                        value={formData.hourlyRate}
                        onChange={handleChange}
                        inputProps={{ min: 0, step: 0.01 }}
                      />
                    </Grid>
                  </>
                )}

                <Grid item xs={12}>
                  <Button
                    type="submit"
                    variant="contained"
                    size="large"
                    disabled={loading}
                    sx={{ mr: 2 }}
                  >
                    {loading ? 'Updating...' : 'Update Profile'}
                  </Button>
                </Grid>
              </Grid>
            </Box>
          </CardContent>
        </Card>

        {user?.role === 'TUTOR' && (
          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Calendly Integration
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Connect your Calendly account to automatically sync your availability and sessions.
              </Typography>

              {user?.calendlyUri ? (
                <Box>
                  <Typography variant="body2" color="success.main" sx={{ mb: 2 }}>
                    ✓ Calendly account connected successfully
                  </Typography>
                  <Button
                    variant="outlined"
                    color="secondary"
                    onClick={() => {
                      // TODO: Implement disconnect functionality
                      setAlert({ show: true, message: 'Disconnect functionality coming soon', severity: 'info' });
                    }}
                  >
                    Disconnect Calendly
                  </Button>
                </Box>
              ) : (
                <Button
                  variant="outlined"
                  color="primary"
                  onClick={handleCalendlyConfig}
                >
                  Configure Calendly
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </Box>
    </Container>
  );
}

export default Profile;
